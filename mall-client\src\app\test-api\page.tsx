'use client';

import React, { useState } from 'react';
import { testCreateOrder, testGetOrders, testBackendConnection, testLogin, testRegister } from '../../utils/test-api';

export default function TestApiPage() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testConnection = async () => {
    setIsLoading(true);
    addResult('开始测试后端连接...');

    try {
      const isConnected = await testBackendConnection();
      if (isConnected) {
        addResult('✅ 后端连接成功');
      } else {
        addResult('❌ 后端连接失败');
      }
    } catch (error) {
      addResult(`❌ 后端连接错误: ${error}`);
    }

    setIsLoading(false);
  };

  const testUserRegister = async () => {
    setIsLoading(true);
    addResult('开始测试用户注册...');

    try {
      const result = await testRegister();
      if (result.success) {
        addResult(`✅ 注册成功: ${result.message}`);
      } else {
        addResult(`❌ 注册失败: ${result.message}`);
      }
    } catch (error) {
      addResult(`❌ 注册错误: ${error}`);
    }

    setIsLoading(false);
  };

  const testUserLogin = async () => {
    setIsLoading(true);
    addResult('开始测试用户登录...');

    try {
      const result = await testLogin();
      if (result.success) {
        addResult(`✅ 登录成功: ${result.message}`);
        addResult(`Token: ${result.token?.substring(0, 20)}...`);
      } else {
        addResult(`❌ 登录失败: ${result.message}`);
      }
    } catch (error) {
      addResult(`❌ 登录错误: ${error}`);
    }

    setIsLoading(false);
  };

  const testOrderCreation = async () => {
    setIsLoading(true);
    addResult('开始测试订单创建...');
    
    try {
      const result = await testCreateOrder();
      addResult(`✅ 订单创建成功: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      addResult(`❌ 订单创建失败: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testOrderRetrieval = async () => {
    setIsLoading(true);
    addResult('开始测试订单获取...');
    
    try {
      const result = await testGetOrders();
      addResult(`✅ 订单获取成功: 找到 ${result.length} 个订单`);
      addResult(`订单详情: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      addResult(`❌ 订单获取失败: ${error}`);
    }
    
    setIsLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">API 测试页面</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* 测试按钮 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">测试功能</h2>
          
          <div className="space-y-4">
            <button
              onClick={testConnection}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '测试中...' : '测试后端连接'}
            </button>

            <button
              onClick={testUserLogin}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '测试中...' : '测试用户登录'}
            </button>

            <button
              onClick={testOrderCreation}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '测试中...' : '测试订单创建'}
            </button>
            
            <button
              onClick={testOrderRetrieval}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '测试中...' : '测试订单获取'}
            </button>
            
            <button
              onClick={clearResults}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              清空结果
            </button>
          </div>
        </div>
        
        {/* 测试结果 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">测试结果</h2>
          
          <div className="bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto">
            {results.length === 0 ? (
              <p className="text-gray-500">暂无测试结果</p>
            ) : (
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-8 text-center">
        <a
          href="/orders"
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          返回订单页面
        </a>
      </div>
    </div>
  );
}
