import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Address } from '../types/order';

// 地址管理狀態接口
interface AddressState {
  addresses: Address[];
  selectedAddressId: number | null;
  
  // 操作方法
  addAddress: (address: Omit<Address, 'id'>) => void;
  updateAddress: (id: number, address: Partial<Address>) => void;
  removeAddress: (id: number) => void;
  setDefaultAddress: (id: number) => void;
  selectAddress: (id: number) => void;
  getDefaultAddress: () => Address | null;
  getSelectedAddress: () => Address | null;
  clearAddresses: () => void;
}

// 生成臨時ID（實際項目中應該由後端生成）
let tempId = 1;

// 創建地址管理 store
export const useAddressStore = create<AddressState>()(
  persist(
    (set, get) => ({
      addresses: [],
      selectedAddressId: null,

      // 添加地址
      addAddress: (address) => {
        const { addresses } = get();
        const newAddress: Address = {
          ...address,
          id: tempId++,
          // 如果是第一個地址，自動設為默認
          isDefault: addresses.length === 0 ? true : address.isDefault
        };

        // 如果新地址設為默認，取消其他地址的默認狀態
        const updatedAddresses = address.isDefault
          ? addresses.map(addr => ({ ...addr, isDefault: false }))
          : addresses;

        set({
          addresses: [...updatedAddresses, newAddress],
          // 如果沒有選中的地址，自動選中新添加的地址
          selectedAddressId: get().selectedAddressId || newAddress.id!
        });
      },

      // 更新地址
      updateAddress: (id, updatedAddress) => {
        const { addresses } = get();
        
        const newAddresses = addresses.map(address => {
          if (address.id === id) {
            const updated = { ...address, ...updatedAddress };
            
            // 如果設為默認地址，取消其他地址的默認狀態
            if (updatedAddress.isDefault) {
              return updated;
            }
            return updated;
          }
          
          // 如果當前更新的地址設為默認，其他地址取消默認
          if (updatedAddress.isDefault) {
            return { ...address, isDefault: false };
          }
          
          return address;
        });

        set({ addresses: newAddresses });
      },

      // 刪除地址
      removeAddress: (id) => {
        const { addresses, selectedAddressId } = get();
        const addressToRemove = addresses.find(addr => addr.id === id);
        const newAddresses = addresses.filter(address => address.id !== id);
        
        // 如果刪除的是默認地址，設置第一個地址為默認
        if (addressToRemove?.isDefault && newAddresses.length > 0) {
          newAddresses[0].isDefault = true;
        }

        set({
          addresses: newAddresses,
          // 如果刪除的是當前選中的地址，重置選中狀態
          selectedAddressId: selectedAddressId === id 
            ? (newAddresses.length > 0 ? newAddresses[0].id! : null)
            : selectedAddressId
        });
      },

      // 設置默認地址
      setDefaultAddress: (id) => {
        const { addresses } = get();
        const newAddresses = addresses.map(address => ({
          ...address,
          isDefault: address.id === id
        }));

        set({ addresses: newAddresses });
      },

      // 選擇地址（用於結算）
      selectAddress: (id) => {
        set({ selectedAddressId: id });
      },

      // 獲取默認地址
      getDefaultAddress: () => {
        const { addresses } = get();
        return addresses.find(address => address.isDefault) || null;
      },

      // 獲取當前選中的地址
      getSelectedAddress: () => {
        const { addresses, selectedAddressId } = get();
        if (!selectedAddressId) return null;
        return addresses.find(address => address.id === selectedAddressId) || null;
      },

      // 清空所有地址
      clearAddresses: () => {
        set({
          addresses: [],
          selectedAddressId: null
        });
      }
    }),
    {
      name: 'address-storage',
      version: 1,
    }
  )
);
