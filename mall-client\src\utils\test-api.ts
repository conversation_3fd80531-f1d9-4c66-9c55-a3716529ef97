// 测试API连接的工具函数

import { orderApi, authApi } from './api';
import { CreateOrderRequest, PaymentMethod } from '../types/order';

// 测试注册新用户
export async function testRegister() {
  try {
    console.log('开始测试用户注册...');
    const registerData = {
      username: 'testuser123',
      password: '123456',
      confirmPassword: '123456',
      email: '<EMAIL>'
    };

    const response = await fetch('http://localhost:8080/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(registerData),
    });

    const result = await response.json();
    console.log('注册结果:', result);
    return result;
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
}

// 测试登录
export async function testLogin() {
  try {
    console.log('开始测试登录...');
    // 先尝试用新注册的用户登录
    try {
      const result = await authApi.login('testuser123', '123456');
      console.log('登录结果:', result);
      if (result.success) {
        return result;
      }
    } catch (error) {
      console.log('新用户登录失败，尝试其他用户');
    }

    // 如果新用户失败，尝试现有用户的不同密码
    const passwords = ['123456', 'password', 'test123', 'test', '123123'];

    for (const password of passwords) {
      try {
        console.log(`尝试密码: ${password}`);
        const result = await authApi.login('test', password);
        console.log('登录结果:', result);
        if (result.success) {
          return result;
        }
      } catch (error) {
        console.log(`密码 ${password} 失败:`, error);
      }
    }

    throw new Error('所有登录尝试都失败');
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}

// 测试订单创建
export async function testCreateOrder() {
  const testOrderData: CreateOrderRequest = {
    items: [
      {
        productId: 1,
        quantity: 2
      }
    ],
    shippingAddressId: 1,
    paymentMethod: PaymentMethod.CREDIT_CARD,
    notes: '测试订单'
  };

  const testAddress = {
    id: 1,
    recipientName: '张三',
    phone: '13800138000',
    province: '广东省',
    city: '深圳市',
    district: '南山区',
    detailAddress: '科技园南区深南大道10000号',
    postalCode: '518000',
    isDefault: true
  };

  try {
    console.log('开始测试订单创建...');
    const result = await orderApi.create(testOrderData, testAddress);
    console.log('订单创建成功:', result);
    return result;
  } catch (error) {
    console.error('订单创建失败:', error);
    throw error;
  }
}

// 测试获取订单列表
export async function testGetOrders() {
  try {
    console.log('开始测试获取订单列表...');
    const result = await orderApi.getByUserId(1);
    console.log('获取订单列表成功:', result);
    return result;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw error;
  }
}

// 测试后端连接
export async function testBackendConnection() {
  try {
    const response = await fetch('http://localhost:8080/api/products');
    if (response.ok) {
      console.log('后端连接正常');
      return true;
    } else {
      console.error('后端连接失败:', response.status);
      return false;
    }
  } catch (error) {
    console.error('后端连接错误:', error);
    return false;
  }
}
