import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Order, OrderStatus, CreateOrderRequest, OrderItem, ShippingInfo, ShippingRequest, Address } from '../types/order';
import { orderApi } from '../utils/api';
import { useAddressStore } from './addressStore';

// 訂單管理狀態接口
interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  isLoading: boolean;
  error: string | null;

  // 發貨信息緩存
  shippingInfos: Map<number, ShippingInfo>; // orderId -> ShippingInfo

  // 操作方法
  createOrder: (orderData: CreateOrderRequest) => Promise<Order>;
  getOrderById: (id: number) => Order | null;
  getUserOrders: (userId?: number) => Order[];
  loadOrdersFromBackend: (userId?: number) => Promise<void>;
  updateOrderStatus: (orderId: number, status: OrderStatus) => void;
  cancelOrder: (orderId: number) => void;
  setCurrentOrder: (order: Order | null) => void;
  clearOrders: () => void;

  // 發貨相關方法
  shipOrder: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrderShipping: (orderId: number) => Promise<ShippingInfo | null>;
  updateOrderShipping: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrdersReadyToShip: () => Order[];

  // 統計方法
  getOrdersByStatus: (status: OrderStatus) => Order[];
  getTotalSpent: () => number;
}

// 生成訂單號
const generateOrderNumber = (): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD${timestamp}${random}`;
};

// 生成臨時ID（實際項目中應該由後端生成）
let tempOrderId = 1;

// 創建訂單管理 store
export const useOrderStore = create<OrderState>()(
  persist(
    (set, get) => ({
      orders: [],
      currentOrder: null,
      isLoading: false,
      error: null,
      shippingInfos: new Map(),

      // 創建訂單
      createOrder: async (orderData: CreateOrderRequest) => {
        set({ isLoading: true, error: null });

        try {
          // 获取选中的收货地址
          const addressStore = useAddressStore.getState();
          const shippingAddress = addressStore.addresses.find(addr => addr.id === orderData.shippingAddressId);

          if (!shippingAddress) {
            throw new Error('請選擇收貨地址');
          }

          // 调用后端API创建订单
          const backendOrder = await orderApi.create(orderData, shippingAddress);

          // 转换后端返回的订单数据为前端格式
          const newOrder: Order = {
            id: backendOrder.id,
            userId: backendOrder.userId,
            orderNumber: backendOrder.orderCode,
            status: backendOrder.status?.statusName as OrderStatus || OrderStatus.PENDING,
            items: backendOrder.orderItems?.map((item: any) => ({
              id: item.id,
              orderId: item.orderId,
              productId: item.productId,
              productName: item.productNameAtPurchase || `商品 ${item.productId}`,
              productImage: '/placeholder.jpg', // 需要从商品信息获取
              price: item.priceAtPurchase || item.price,
              quantity: item.quantity,
              subtotal: (item.priceAtPurchase || item.price) * item.quantity
            })) || [],
            subtotal: backendOrder.totalAmount - 10, // 简化计算，实际应该从后端获取详细信息
            shippingFee: 10, // 简化处理
            discount: 0,
            totalAmount: backendOrder.totalAmount,
            shippingAddress,
            paymentMethod: orderData.paymentMethod,
            paymentStatus: backendOrder.paymentStatus as 'PENDING' | 'COMPLETED' | 'FAILED',
            createdAt: backendOrder.createdAt || new Date().toISOString(),
            updatedAt: backendOrder.updatedAt || new Date().toISOString(),
            notes: backendOrder.notes
          };

          const { orders } = get();
          set({
            orders: [newOrder, ...orders],
            currentOrder: newOrder,
            isLoading: false
          });

          return newOrder;
        } catch (error) {
          console.error('創建訂單失敗:', error);
          set({
            error: error instanceof Error ? error.message : '創建訂單失敗',
            isLoading: false
          });
          throw error;
        }
      },

      // 根據ID獲取訂單
      getOrderById: (id: number) => {
        const { orders } = get();
        return orders.find(order => order.id === id) || null;
      },

      // 獲取用戶訂單
      getUserOrders: (userId?: number) => {
        const { orders } = get();
        // 如果沒有傳入userId，返回所有訂單（模擬當前用戶的訂單）
        return orders;
      },

      // 从后端加载订单
      loadOrdersFromBackend: async (userId: number = 1) => {
        set({ isLoading: true, error: null });

        try {
          // 调用后端API获取用户订单
          const backendOrders = await orderApi.getByUserId(userId);

          // 转换后端订单数据为前端格式
          const orders: Order[] = backendOrders.map((backendOrder: any) => ({
            id: backendOrder.id,
            userId: backendOrder.userId,
            orderNumber: backendOrder.orderCode,
            status: backendOrder.status?.statusName as OrderStatus || OrderStatus.PENDING,
            items: backendOrder.orderItems?.map((item: any) => ({
              id: item.id,
              orderId: item.orderId,
              productId: item.productId,
              productName: item.productNameAtPurchase || `商品 ${item.productId}`,
              productImage: '/placeholder.jpg',
              price: item.priceAtPurchase || item.price,
              quantity: item.quantity,
              subtotal: (item.priceAtPurchase || item.price) * item.quantity
            })) || [],
            subtotal: backendOrder.totalAmount - 10, // 简化计算
            shippingFee: 10,
            discount: 0,
            totalAmount: backendOrder.totalAmount,
            shippingAddress: {
              id: 1,
              recipientName: '收件人',
              phone: '13800138000',
              province: '廣東省',
              city: '深圳市',
              district: '南山區',
              detailAddress: backendOrder.shippingAddressLine1 || '详细地址',
              isDefault: false
            }, // 需要解析后端地址信息
            paymentMethod: backendOrder.paymentMethod,
            paymentStatus: backendOrder.paymentStatus as 'PENDING' | 'COMPLETED' | 'FAILED',
            createdAt: backendOrder.createdAt || new Date().toISOString(),
            updatedAt: backendOrder.updatedAt || new Date().toISOString(),
            notes: backendOrder.notes
          }));

          set({
            orders,
            isLoading: false
          });
        } catch (error) {
          console.error('加载订单失败:', error);
          set({
            error: error instanceof Error ? error.message : '加载订单失败',
            isLoading: false
          });
        }
      },

      // 更新訂單狀態
      updateOrderStatus: (orderId: number, status: OrderStatus) => {
        const { orders } = get();
        const updatedOrders = orders.map(order => {
          if (order.id === orderId) {
            const updatedOrder = {
              ...order,
              status,
              updatedAt: new Date().toISOString()
            };
            
            // 如果狀態變為已付款，更新支付狀態和時間
            if (status === OrderStatus.PAID) {
              updatedOrder.paymentStatus = 'COMPLETED';
              updatedOrder.paidAt = new Date().toISOString();
            }
            
            return updatedOrder;
          }
          return order;
        });

        set({ orders: updatedOrders });
      },

      // 取消訂單
      cancelOrder: (orderId: number) => {
        get().updateOrderStatus(orderId, OrderStatus.CANCELLED);
      },

      // 設置當前訂單
      setCurrentOrder: (order: Order | null) => {
        set({ currentOrder: order });
      },

      // 清空訂單
      clearOrders: () => {
        set({
          orders: [],
          currentOrder: null,
          error: null
        });
      },

      // 根據狀態獲取訂單
      getOrdersByStatus: (status: OrderStatus) => {
        const { orders } = get();
        return orders.filter(order => order.status === status);
      },

      // 計算總消費
      getTotalSpent: () => {
        const { orders } = get();
        return orders
          .filter(order => order.status !== OrderStatus.CANCELLED)
          .reduce((total, order) => total + order.totalAmount, 0);
      },

      // 發貨相關方法
      shipOrder: async (orderId: number, shippingData: ShippingRequest) => {
        set({ isLoading: true, error: null });

        try {
          // 模擬API調用
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 實際應該調用後端API
          // const response = await fetch(`/api/orders/${orderId}/ship`, {
          //   method: 'POST',
          //   headers: { 'Content-Type': 'application/json' },
          //   body: JSON.stringify(shippingData)
          // });
          // const shippingInfo = await response.json();

          // 模擬創建發貨信息
          const now = new Date().toISOString();
          const shippingInfo: ShippingInfo = {
            id: Date.now(),
            orderId,
            trackingNumber: shippingData.trackingNumber,
            carrier: shippingData.carrier,
            shippingMethod: shippingData.shippingMethod,
            shippedAt: now,
            estimatedDelivery: shippingData.estimatedDays
              ? new Date(Date.now() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()
              : undefined,
            notes: shippingData.notes,
            createdAt: now,
            updatedAt: now
          };

          // 更新發貨信息緩存
          const { shippingInfos } = get();
          const newShippingInfos = new Map(shippingInfos);
          newShippingInfos.set(orderId, shippingInfo);

          // 更新訂單狀態為已發貨
          get().updateOrderStatus(orderId, OrderStatus.SHIPPED);

          set({
            shippingInfos: newShippingInfos,
            isLoading: false
          });

          return shippingInfo;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '發貨失敗',
            isLoading: false
          });
          throw error;
        }
      },

      getOrderShipping: async (orderId: number) => {
        const { shippingInfos } = get();

        // 先檢查緩存
        const cachedShipping = shippingInfos.get(orderId);
        if (cachedShipping) {
          return cachedShipping;
        }

        try {
          // 實際應該調用後端API
          // const response = await fetch(`/api/orders/${orderId}/shipping`);
          // if (response.ok) {
          //   const shippingInfo = await response.json();
          //   // 更新緩存
          //   const newShippingInfos = new Map(shippingInfos);
          //   newShippingInfos.set(orderId, shippingInfo);
          //   set({ shippingInfos: newShippingInfos });
          //   return shippingInfo;
          // }

          return null;
        } catch (error) {
          console.error('獲取發貨信息失敗:', error);
          return null;
        }
      },

      updateOrderShipping: async (orderId: number, shippingData: ShippingRequest) => {
        set({ isLoading: true, error: null });

        try {
          // 模擬API調用
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 實際應該調用後端API
          // const response = await fetch(`/api/orders/${orderId}/shipping`, {
          //   method: 'PUT',
          //   headers: { 'Content-Type': 'application/json' },
          //   body: JSON.stringify(shippingData)
          // });
          // const shippingInfo = await response.json();

          // 模擬更新發貨信息
          const { shippingInfos } = get();
          const existingShipping = shippingInfos.get(orderId);

          if (!existingShipping) {
            throw new Error('該訂單尚未發貨');
          }

          const updatedShipping: ShippingInfo = {
            ...existingShipping,
            trackingNumber: shippingData.trackingNumber,
            carrier: shippingData.carrier,
            shippingMethod: shippingData.shippingMethod,
            notes: shippingData.notes,
            estimatedDelivery: shippingData.estimatedDays
              ? new Date(new Date(existingShipping.shippedAt).getTime() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()
              : existingShipping.estimatedDelivery,
            updatedAt: new Date().toISOString()
          };

          // 更新緩存
          const newShippingInfos = new Map(shippingInfos);
          newShippingInfos.set(orderId, updatedShipping);

          set({
            shippingInfos: newShippingInfos,
            isLoading: false
          });

          return updatedShipping;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '更新發貨信息失敗',
            isLoading: false
          });
          throw error;
        }
      },

      getOrdersReadyToShip: () => {
        const { orders } = get();
        return orders.filter(order =>
          order.status === OrderStatus.PAID || order.status === OrderStatus.PROCESSING
        );
      }
    }),
    {
      name: 'order-storage',
      version: 1,
    }
  )
);
